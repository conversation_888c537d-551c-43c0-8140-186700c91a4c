<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="openrouter-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="11" fill="url(#openrouter-gradient)" opacity="0.1"/>
  <circle cx="12" cy="12" r="11" fill="none" stroke="url(#openrouter-gradient)" stroke-width="0.5" opacity="0.3"/>
  <title>OpenRouter</title>
  <g transform="translate(3, 3) scale(0.75)">
    <path d="M12 2L2 7v10l10 5 10-5V7L12 2z" fill="none" stroke="url(#openrouter-gradient)" stroke-width="2"/>
    <path d="M12 2v20M2 7l10 5 10-5" stroke="url(#openrouter-gradient)" stroke-width="2"/>
    <circle cx="12" cy="12" r="2" fill="url(#openrouter-gradient)"/>
  </g>
</svg>