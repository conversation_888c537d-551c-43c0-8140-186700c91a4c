<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="generic-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="11" fill="url(#generic-gradient)" opacity="0.1"/>
  <circle cx="12" cy="12" r="11" fill="none" stroke="url(#generic-gradient)" stroke-width="0.5" opacity="0.3"/>
  <title>AI Agent</title>
  <g transform="translate(3, 3) scale(0.75)">
    <!-- AI Brain/Head -->
    <circle cx="12" cy="8" r="5" fill="none" stroke="url(#generic-gradient)" stroke-width="2"/>
    <circle cx="10" cy="7" r="1" fill="url(#generic-gradient)"/>
    <circle cx="14" cy="7" r="1" fill="url(#generic-gradient)"/>
    <path d="M9 9.5c1 1 3 1 4 0" stroke="url(#generic-gradient)" stroke-width="1.5" stroke-linecap="round"/>

    <!-- Neural Network Pattern -->
    <circle cx="6" cy="16" r="1.5" fill="url(#generic-gradient)" opacity="0.7"/>
    <circle cx="12" cy="18" r="1.5" fill="url(#generic-gradient)" opacity="0.7"/>
    <circle cx="18" cy="16" r="1.5" fill="url(#generic-gradient)" opacity="0.7"/>

    <!-- Connecting Lines -->
    <path d="M12 13l-5 2M12 13l0 4M12 13l5 2" stroke="url(#generic-gradient)" stroke-width="1.5" opacity="0.6" stroke-linecap="round"/>
  </g>
</svg>