<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="together-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F7931E;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="11" fill="url(#together-gradient)" opacity="0.1"/>
  <circle cx="12" cy="12" r="11" fill="none" stroke="url(#together-gradient)" stroke-width="0.5" opacity="0.3"/>
  <title>Together AI</title>
  <g transform="translate(2, 2) scale(0.83)">
    <!-- Together AI Logo - Interconnected nodes representing collaboration -->
    <circle cx="8" cy="6" r="2.5" fill="url(#together-gradient)"/>
    <circle cx="16" cy="6" r="2.5" fill="url(#together-gradient)"/>
    <circle cx="12" cy="12" r="3" fill="url(#together-gradient)"/>
    <circle cx="6" cy="18" r="2.5" fill="url(#together-gradient)"/>
    <circle cx="18" cy="18" r="2.5" fill="url(#together-gradient)"/>

    <!-- Connection lines showing "together" concept -->
    <path d="M8 8l4 2M16 8l-4 2M12 15l-5 2M12 15l5 2" stroke="url(#together-gradient)" stroke-width="2.5" stroke-linecap="round" opacity="0.8"/>
    <path d="M10 6l4 0M6 16l10 0" stroke="url(#together-gradient)" stroke-width="1.5" stroke-linecap="round" opacity="0.6"/>

    <!-- Central hub emphasis -->
    <circle cx="12" cy="12" r="1" fill="white"/>
  </g>
</svg>