<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="together-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="11" fill="url(#together-gradient)" opacity="0.1"/>
  <circle cx="12" cy="12" r="11" fill="none" stroke="url(#together-gradient)" stroke-width="0.5" opacity="0.3"/>
  <title>Serving Platform</title>
  <g transform="translate(3, 3) scale(0.75)">
    <!-- Server/Platform icon -->
    <rect x="4" y="6" width="16" height="3" rx="1" fill="url(#together-gradient)" opacity="0.8"/>
    <rect x="4" y="10.5" width="16" height="3" rx="1" fill="url(#together-gradient)" opacity="0.8"/>
    <rect x="4" y="15" width="16" height="3" rx="1" fill="url(#together-gradient)" opacity="0.8"/>

    <!-- Connection dots -->
    <circle cx="7" cy="7.5" r="0.5" fill="white"/>
    <circle cx="9" cy="7.5" r="0.5" fill="white"/>
    <circle cx="7" cy="12" r="0.5" fill="white"/>
    <circle cx="9" cy="12" r="0.5" fill="white"/>
    <circle cx="7" cy="16.5" r="0.5" fill="white"/>
    <circle cx="9" cy="16.5" r="0.5" fill="white"/>
  </g>
</svg>