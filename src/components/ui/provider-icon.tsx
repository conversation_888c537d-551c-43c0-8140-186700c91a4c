import React from 'react';
import Image from 'next/image';

interface ProviderIconProps {
  provider: string;
  width?: number;
  height?: number;
  className?: string;
}

const ProviderIcon: React.FC<ProviderIconProps> = ({
  provider,
  width = 24,
  height = 24,
  className = '',
}) => {
  // Provider name mapping to handle different variations
  const providerMapping: { [key: string]: string } = {
    'alibaba': 'alibaba',
    'alibaba-cloud': 'alibaba',
    'qwen': 'alibaba',
    'openai': 'openai',
    'anthropic': 'anthropic',
    'claude': 'anthropic',
    'google': 'google',
    'gemini': 'gemini',
    'meta': 'meta',
    'llama': 'meta',
    'mistral': 'mistral',
    'mistral-ai': 'mistral',
    'deepseek': 'deepseek',
    'xai': 'xai',
    'grok': 'xai',
    'cohere': 'cohere',
    'perplexity': 'perplexity',
    'perplexity-ai': 'perplexity',
    'groq': 'groq',
    'nova': 'nova',
    'openrouter': 'openrouter',
    'together': 'together',
    'together-ai': 'together',
  };

  // Normalize provider name
  const normalizedProvider = provider.toLowerCase().replace(/\s+/g, '-');
  const logoName = providerMapping[normalizedProvider] || 'default';
  const logoPath = `/logos/providers/${logoName}.svg`;

  // Add styles to brighten and enhance the logos with circular backgrounds
  const enhancedClassName = `${className}
    contrast-110
    saturate-110
    brightness-105
    hover:brightness-115
    hover:saturate-125
    hover:scale-105
    transition-all
    duration-200
    drop-shadow-md
    rounded-full
  `.replace(/\s+/g, ' ').trim();

  return (
    <Image
      src={logoPath}
      alt={`${provider} Logo`}
      width={width}
      height={height}
      className={enhancedClassName}
      unoptimized
      style={{
        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15))',
        borderRadius: '50%',
      }}
      onError={(e) => {
        // Fallback to default logo if the specific logo fails to load
        const target = e.target as HTMLImageElement;
        if (target.src !== '/logos/providers/default.svg') {
          target.src = '/logos/providers/default.svg';
        }
      }}
    />
  );
};

export default ProviderIcon; 