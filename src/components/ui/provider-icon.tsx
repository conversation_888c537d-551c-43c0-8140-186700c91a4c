import React from 'react';
import Image from 'next/image';

interface ProviderIconProps {
  provider: string;
  width?: number;
  height?: number;
  className?: string;
}

const ProviderIcon: React.FC<ProviderIconProps> = ({
  provider,
  width = 24,
  height = 24,
  className = '',
}) => {
  // Model provider mapping - prioritizes actual model creators over serving platforms
  const getModelProvider = (provider: string): string => {
    const normalized = provider.toLowerCase().replace(/\s+/g, '-');

    // Model-based detection (prioritize actual model creators)
    if (normalized.includes('llama') || normalized.includes('meta')) return 'meta';
    if (normalized.includes('gpt') || normalized.includes('openai')) return 'openai';
    if (normalized.includes('claude') || normalized.includes('anthropic')) return 'anthropic';
    if (normalized.includes('gemini') || normalized.includes('bard')) return 'gemini';
    if (normalized.includes('qwen') || normalized.includes('alibaba')) return 'alibaba';
    if (normalized.includes('mistral')) return 'mistral';
    if (normalized.includes('deepseek')) return 'deepseek';
    if (normalized.includes('grok') || normalized.includes('xai')) return 'xai';
    if (normalized.includes('cohere')) return 'cohere';
    if (normalized.includes('perplexity')) return 'perplexity';
    if (normalized.includes('nova')) return 'nova';

    // Direct provider mapping for when we have explicit provider names
    const providerMapping: { [key: string]: string } = {
      'alibaba': 'alibaba',
      'alibaba-cloud': 'alibaba',
      'qwen': 'alibaba',
      'openai': 'openai',
      'anthropic': 'anthropic',
      'claude': 'anthropic',
      'google': 'google',
      'gemini': 'gemini',
      'meta': 'meta',
      'llama': 'meta',
      'mistral': 'mistral',
      'mistral-ai': 'mistral',
      'deepseek': 'deepseek',
      'xai': 'xai',
      'grok': 'xai',
      'cohere': 'cohere',
      'perplexity': 'perplexity',
      'perplexity-ai': 'perplexity',
      'groq': 'groq',
      'nova': 'nova',
      // Serving platforms fall back to generic when no model is detected
      'openrouter': 'openrouter',
      'together': 'openrouter', // Use generic for serving platforms
      'together-ai': 'openrouter',
    };

    return providerMapping[normalized] || 'default';
  };

  // Get the appropriate logo based on model provider (not serving platform)
  const logoName = getModelProvider(provider);
  const logoPath = `/logos/providers/${logoName}.svg`;

  // Add styles to brighten and enhance the logos with circular backgrounds
  const enhancedClassName = `${className}
    contrast-110
    saturate-110
    brightness-105
    hover:brightness-115
    hover:saturate-125
    hover:scale-105
    transition-all
    duration-200
    drop-shadow-md
    rounded-full
  `.replace(/\s+/g, ' ').trim();

  return (
    <Image
      src={logoPath}
      alt={`${provider} Logo`}
      width={width}
      height={height}
      className={enhancedClassName}
      unoptimized
      style={{
        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15))',
        borderRadius: '50%',
      }}
      onError={(e) => {
        // Fallback to default logo if the specific logo fails to load
        const target = e.target as HTMLImageElement;
        if (target.src !== '/logos/providers/default.svg') {
          target.src = '/logos/providers/default.svg';
        }
      }}
    />
  );
};

export default ProviderIcon; 